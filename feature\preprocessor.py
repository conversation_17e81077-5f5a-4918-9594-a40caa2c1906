#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author: Tencent AI Arena Authors

"""

import numpy as np
import math
from agent_ppo.feature.definition import RelativeDistance, RelativeDirection, DirectionAngles, reward_process

# ----------------  utils  ----------------
def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)

def angle_to_vec(direction: str) -> tuple[float, float]:
    theta = math.radians(DirectionAngles[RelativeDirection[direction]])
    return math.cos(theta), math.sin(theta)

def dist_to_scalar(l2_dist_str: str) -> float:
    mapping = {
        "VerySmall": 2.5, "Small": 7.5, "Medium": 15,
        "Large": 25, "VeryLarge": 40
    }
    return mapping[l2_dist_str] / 40.0  # 歸一化到 0-1
# -----------------------------------------

class Preprocessor:
    def __init__(self) -> None:
        # 从8修改成16，代表8个移动维度，8个闪现维度
        self.move_action_num = 16
        self.reset()

    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0))
        self.end_pos = None
        self.is_end_pos_found = False
        self.end_pos_dir = None
        self.end_pos_dis = None
        self.history_pos = []
        self.bad_move_ids = set()
        # 闪现是否可用，初始化为True
        self.is_flash_usable = True
        # 初始化记忆矩阵
        self.global_memory_map = np.zeros((128,128), dtype=np.float32)
        self.local_memory_map = np.zeros((11,11), dtype=np.float32)

        # 初始化organs特征系统
        self._reset_organs_features()
        self.prev_end_dist = 0.0

        self.prev_end_dist = 0.0  # 新增: 記錄上一步的end_dist
        # 新增: 記錄上一步的buff_count
        self.prev_buff_count = 0
        self.cur_buff_count = 0
        # 初始化寶箱距離列表為999*13
        self.treasure_distance_list = [999.0] * 13
        self.prev_treasure_distance_list = [999.0] * 13
        # 新增: 記錄最近距離寶箱的index
        self.nearest_treasure_index = -1
        self.prev_nearest_treasure_index = -1
        # 初始化終點位置
        self.real_end_pos = None
        self.real_end_dist = 0.0
        self.prev_real_end_dist = 0.0
        self.bad_move_penalty = 0.0
        self.is_use_flash = False
        self.flash_hit_wall_penalty = 0.0
        
        # 重置 TreasureLocker（每個 episode 重新創建）
        from agent_ppo.feature.definition import locker
        locker.locked_id = None
        locker.prev_dist = None

    def _reset_organs_features(self):
        """重置organs原始特征"""
        # 简化版: 15個 organ, 每個 1 維 (只保留status信息)
        self.organs_features = np.zeros((15, 1), dtype=np.float32)

    def _generate_local_flags(self, organs, map_info):
        """
        根据英雄当前位置和视野内的物件生成11x11的局部地图标志
        简化版本：直接创建flattened数组，避免中间矩阵
        """
        # 直接初始化flattened数组 (11*11 = 121维)
        self.treasure_flag = np.zeros(121, dtype=np.float32)
        self.end_flag = np.zeros(121, dtype=np.float32)
        self.obstacle_flag = np.zeros(121, dtype=np.float32)
        self.buff_flag = np.zeros(121, dtype=np.float32)

        # 遍历 map_info 并直接填充flattened数组
        for r, row_data in enumerate(map_info):
            for c, value in enumerate(row_data['values']):
                # 计算flattened索引
                flat_idx = r * 11 + c
                
                # 根据value值直接设置对应的flag
                if value == 4:      # 宝箱
                    self.treasure_flag[flat_idx] = 1.0
                elif value == 3:    # 终点
                    self.end_flag[flat_idx] = 1.0
                elif value == 0:    # 障碍物
                    self.obstacle_flag[flat_idx] = 1.0
                elif value == 6:    # 加速增益
                    self.buff_flag[flat_idx] = 1.0

    def memory_update(self, cur_pos):
        """
        记忆矩阵更新
        """
        # 全局记忆矩阵
        x,z = cur_pos
        z = 127 - z
        current_value = self.global_memory_map[z, x]
        self.global_memory_map[z, x] = min(1.0, current_value + 0.1)

        # 局部记忆矩阵
        # 计算在全局地图上的源区域边界
        src_top = max(0, z - 5)
        src_bottom = min(128, z + 6)
        src_left = max(0, x - 5)
        src_right = min(128, x + 6)

        # 计算在局部地图上的目标区域边界
        dst_top = src_top - (z - 5)
        dst_bottom = src_bottom - (z - 5)
        dst_left = src_left - (x - 5)
        dst_right = src_right - (x - 5)

        # 从全局地图复制有效区域到局部地图
        self.local_memory_map[dst_top:dst_bottom, dst_left:dst_right] = self.global_memory_map[src_top:src_bottom, src_left:src_right]
        self.memory_flag = self.local_memory_map.flatten()

    def _get_pos_feature(self, found, cur_pos, target_pos):
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        feature = np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            ),
        )
        return feature

    def pb2struct(self, frame_state, last_action):
        obs, extra_info = frame_state
        self.step_no = obs["frame_state"]["step_no"]

        # 注意：这里暂时不保存，在pb2struct函数最后保存



        hero = obs["frame_state"]["heroes"][0]
        map_info = obs["map_info"]  # 障碍物


        self.is_flash_usable = (hero['talent']['cooldown'] == 0)

        # 获取当前位置
        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])

        # 保存上一個step的寶箱距離列表
        self.prev_treasure_distance_list = self.treasure_distance_list.copy() if hasattr(self, 'treasure_distance_list') else [999.0] * 13
        # 保存上一個step的buff_count
        self.prev_buff_count = self.cur_buff_count
        # 保存上一個step的最近寶箱index
        self.prev_nearest_treasure_index = self.nearest_treasure_index if hasattr(self, 'nearest_treasure_index') else -1
        # 創建長度為13的數組來存儲寶箱距離
        treasure_distance_list = [999.0] * 13
        
        # 計算寶箱距離列表
        if extra_info is not None:
            # 保存上一個step的 real_end_dist
            self.prev_real_end_dist = self.real_end_dist
            # 解析當前end_pos
            if 'game_info' in extra_info and 'end_pos' in extra_info['game_info']:
                end_pos_dict = extra_info['game_info']['end_pos']
                self.real_end_pos = (end_pos_dict.get('x', 0), end_pos_dict.get('z', 0))
            # 計算 real_end_dist (使用曼哈頓距離)
            if self.real_end_pos is not None:
                agent_x = extra_info['game_info']['pos']['x']
                agent_z = extra_info['game_info']['pos']['z']
                self.real_end_dist = abs(agent_x - self.real_end_pos[0]) + abs(agent_z - self.real_end_pos[1])
            else:
                self.real_end_dist = 0.0
            # 計算寶箱距離 (使用曼哈頓距離)
            agent_x = extra_info['game_info']['pos']['x']
            agent_z = extra_info['game_info']['pos']['z']
            
            # 記錄有效寶箱的距離和index，用於找到最近的寶箱
            valid_treasures = []
            
            for organ in extra_info['frame_state']['organs']:
                if organ['sub_type'] == 1:
                    config_id = organ['config_id']
                    if organ['status'] == 0:
                        treasure_distance_list[config_id - 1] = -1
                        continue
                    treasure_x = organ['pos']['x']
                    treasure_z = organ['pos']['z']
                    distance = abs(treasure_x - agent_x) + abs(treasure_z - agent_z)
                    treasure_distance_list[config_id - 1] = distance  # config_id從1開始，數組索引從0開始
                    valid_treasures.append((distance, config_id - 1))
            
            # 找到最近距離的寶箱index
            if valid_treasures:
                min_distance, min_index = min(valid_treasures, key=lambda x: x[0])
                self.nearest_treasure_index = min_index
            else:
                self.nearest_treasure_index = -1
        else:
            # 如果沒有game_info，設置為默認值
            self.nearest_treasure_index = -1
                
        self.treasure_distance_list = treasure_distance_list
        
        # 获取当前buff_count
        if extra_info is not None and 'score_info' in frame_state[0]:
            self.cur_buff_count = frame_state[0]['score_info'].get('buff_count', 0)
        else:
            self.cur_buff_count = 0

        # 生成基于英雄视野的11x11局部地图标志
        self._generate_local_flags(obs["frame_state"]["organs"], map_info)

        # 计算top-3物件特征
        self.top3_objects_feature = self.get_top3_objects_feature(obs["frame_state"]["organs"], self.cur_pos, frame_state)

        # 更新记忆矩阵
        self.memory_update(self.cur_pos)

        # End position
        # 终点位置
        for organ in obs["frame_state"]["organs"]:
            if organ["sub_type"] == 4:
                end_pos_dis = RelativeDistance[organ["relative_pos"]["l2_distance"]]
                end_pos_dir = RelativeDirection[organ["relative_pos"]["direction"]]
                if organ["status"] != -1:
                    self.end_pos = (organ["pos"]["x"], organ["pos"]["z"])
                    self.is_end_pos_found = True
                # if end_pos is not found, use relative position to predict end_pos
                # 如果终点位置未找到，使用相对位置预测终点位置
                elif (not self.is_end_pos_found) and (
                    self.end_pos is None
                    or self.step_no % 100 == 0
                    or self.end_pos_dir != end_pos_dir
                    or self.end_pos_dis != end_pos_dis
                ):
                    distance = end_pos_dis * 20
                    theta = DirectionAngles[end_pos_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))

                    self.end_pos = (
                        max(0, min(128, round(self.cur_pos[0] + delta_x))),
                        max(0, min(128, round(self.cur_pos[1] + delta_z))),
                    )

                    self.end_pos_dir = end_pos_dir
                    self.end_pos_dis = end_pos_dis

        '''
        message RealmOrgan {
        int32 sub_type = 1; // 物件类型，1代表宝箱, 2代表加速buff,3代表起点,4代表终点
        int32 config_id = 2; // 物件id 0代表buff，1~13代表宝箱 21代表起点, 22代表终点
        int32 status = 3; // 0表示不可获取，1表示可获取, -1表示视野外
        Position pos = 4; // 物件位置坐标
        int32 cooldown = 5;                // 物件剩余冷却时间
        RelativePosition relative_pos = 6; // 物件相对位置
        }
        '''

        # 重置organs特征矩阵
        self.organs_features.fill(0)  # 全部初始化为0

        # 遍历organs数据，进行简化的特征编码
        for organ in obs["frame_state"]["organs"]:
            config_id = organ["config_id"]
            sub_type = organ["sub_type"]
            status = organ["status"]

            # 确定存储索引
            if sub_type == 4:  # 终点
                organ_idx = 14
            elif 0 <= config_id < 14:  # 宝箱和buff
                organ_idx = config_id
            else:
                continue  # 跳过无效的organ

            # 简化特征向量 (1维): 只保留status信息
            # status: -1,0 = 0, 1 = 1
            if status == 1:  # 可获取
                self.organs_features[organ_idx, 0] = 1.0
            else:  # status == -1 或 0
                self.organs_features[organ_idx, 0] = 0.0




        # History position
        # 历史位置
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

            

        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)
        self.feature_end_pos = self._get_pos_feature(self.is_end_pos_found, self.cur_pos, self.end_pos)

        # History position feature
        # 历史位置特征
        self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, self.history_pos[0])

        self.move_usable = True
        self.last_action = last_action

        # 在pb2struct函数最后保存当前步的obs和extra_info
        self.obs = obs  # 当前步的obs
        self.extra_info = extra_info  # 当前步的extra_info

    def process(self, frame_state, last_action, is_exploit=False):
        # 在处理新数据之前，保存上一步的obs和extra_info
        self.prev_obs = getattr(self, 'obs', None)
        self.prev_extra_info = getattr(self, 'extra_info', None)

        self.pb2struct(frame_state, last_action)
        #only for reward, eval is None frame_state[1]


        # 1. 準備【純淨的】向量特徵 (Purified Vector Feature)
        vector_feature = np.concatenate([
            self.cur_pos_norm,                     # 2維
            self.feature_history_pos,              # 6維
            self.organs_features.flatten(),        # 15維 (简化后)
            self.top3_objects_feature.flatten(),  # 18維 (新增)
        ])  # 新的總維度: 41 (2+6+15+18)

        # 2. 準備【空間結構的】地圖特徵 (Spatial Map Feature)
        map_features = np.stack([
            self.treasure_flag.reshape(11, 11),
            self.obstacle_flag.reshape(11, 11),
            self.buff_flag.reshape(11, 11),
            self.end_flag.reshape(11, 11),
            self.local_memory_map
        ], axis=0)  # 輸出形狀: (5, 11, 11)

        # 3. 準備合法動作
        legal_action = self.get_legal_action()

        if is_exploit:
            return (
                vector_feature,
                map_features,
                legal_action
            )
        else:
            # 4. 準備獎勵
            reward = reward_process(self.obs, self.extra_info, self.prev_obs, self.prev_extra_info, self.local_memory_map)

            # 返回清晰分離的四個部分
            return (
                vector_feature,  # np.array, shape (41,)
                map_features,    # np.array, shape (5, 11, 11)
                legal_action,
                reward,
            )

    def get_legal_action(self):
        if (
            self.last_action > -1
            and abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 1e-4
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 1e-4
        ):
            self.bad_move_ids.add(self.last_action)
            self.bad_move_penalty = -1
        else:
            self.bad_move_ids.clear()
            self.bad_move_penalty = 0.0
            
        # 2. 以基礎移動能力初始化合法動作列表
        legal_action = [self.move_usable] * self.move_action_num

        # 3. 疊加閃現冷卻的限制
        if not self.is_flash_usable:
            # 如果閃現不可用，將對應的動作（8到15）設為 False
            for i in range(8, 16):
                legal_action[i] = False

        # 4. 疊加無效移動的限制
        for move_id in self.bad_move_ids:
            if 0 <= move_id < self.move_action_num:
                legal_action[move_id] = False

        # 如果所有動作都被禁用了，重置 bad_move_ids 允許智能體嘗試脫困
        if not any(legal_action):
            self.bad_move_ids.clear()
            # 重新生成一次，這次不再考慮 bad_move_ids
            legal_action = [self.move_usable] * self.move_action_num
            if not self.is_flash_usable:
                for i in range(8, 16):
                    legal_action[i] = False
            
        return legal_action

    def calculate_target_distances_with_prediction(self, organs, cur_pos):

        target_distances = [999.0] * 15  # 初始化15个元素的距离 (13宝箱 + 1buff + 1终点)
        target_statuses = [-1] * 15      # 初始化15个元素的status (13宝箱 + 1buff + 1终点)
        target_relative_x = [0.0] * 15   # 初始化15个元素的relative_x
        target_relative_y = [0.0] * 15   # 初始化15个元素的relative_y
        
        for organ in organs:
            if organ["sub_type"] == 1:  # 宝箱类型
                config_id = organ["config_id"]
                    
                status = organ["status"]
                treasure_idx = config_id    # 1-13為treasure
                target_statuses[treasure_idx] = status  # 记录status
                
                if status == 0:  # 不可获取
                    target_distances[treasure_idx] = -1
                    continue
                    
                # 如果状态为1（可获取），使用明确的位置信息
                if status == 1:
                    treasure_x = organ["pos"]["x"]
                    treasure_z = organ["pos"]["z"]
                    # 计算曼哈顿距离
                    distance = abs(treasure_x - cur_pos[0]) + abs(treasure_z - cur_pos[1])
                    target_distances[treasure_idx] = distance
                    # 计算相对位置
                    target_relative_x[treasure_idx] = (treasure_x - cur_pos[0]) / 128.0
                    target_relative_y[treasure_idx] = (treasure_z - cur_pos[1]) / 128.0
                    
                # 如果状态为-1（视野外），使用相对位置预测
                elif status == -1:
                    relative_pos = organ["relative_pos"]
                    treasure_dir = RelativeDirection[relative_pos["direction"]]
                    treasure_dis = RelativeDistance[relative_pos["l2_distance"]]
                    
                    # 使用与end_pos预测相同的逻辑
                    distance = treasure_dis * 20
                    theta = DirectionAngles[treasure_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))
                    
                    # 预测宝箱位置
                    predicted_x = max(0, min(128, round(cur_pos[0] + delta_x)))
                    predicted_z = max(0, min(128, round(cur_pos[1] + delta_z)))
                    
                    # 计算曼哈顿距离
                    distance = abs(predicted_x - cur_pos[0]) + abs(predicted_z - cur_pos[1])
                    target_distances[treasure_idx] = distance
                    # 计算相对位置
                    target_relative_x[treasure_idx] = delta_x / 128.0
                    target_relative_y[treasure_idx] = delta_z / 128.0
                    
            elif organ["sub_type"] == 4:  # 终点类型
                status = organ["status"]
                end_idx = 14  # 终点固定在第14个位置
                target_statuses[end_idx] = status  # 记录status
                
                if status == 0:  # 不可获取
                    target_distances[end_idx] = -1
                    continue
                    
                # 如果状态为1（可获取），使用明确的位置信息
                if status == 1:
                    end_x = organ["pos"]["x"]
                    end_z = organ["pos"]["z"]
                    # 计算曼哈顿距离
                    distance = abs(end_x - cur_pos[0]) + abs(end_z - cur_pos[1])
                    target_distances[end_idx] = distance
                    # 计算相对位置
                    target_relative_x[end_idx] = (end_x - cur_pos[0]) / 128.0
                    target_relative_y[end_idx] = (end_z - cur_pos[1]) / 128.0
                    
                # 如果状态为-1（视野外），使用相对位置预测
                elif status == -1:
                    relative_pos = organ["relative_pos"]
                    end_dir = RelativeDirection[relative_pos["direction"]]
                    end_dis = RelativeDistance[relative_pos["l2_distance"]]
                    
                    # 使用与end_pos预测相同的逻辑
                    distance = end_dis * 20
                    theta = DirectionAngles[end_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))
                    
                    # 预测终点位置
                    predicted_x = max(0, min(128, round(cur_pos[0] + delta_x)))
                    predicted_z = max(0, min(128, round(cur_pos[1] + delta_z)))
                    
                    # 计算曼哈顿距离
                    distance = abs(predicted_x - cur_pos[0]) + abs(predicted_z - cur_pos[1])
                    target_distances[end_idx] = distance
                    # 计算相对位置
                    target_relative_x[end_idx] = delta_x / 128.0
                    target_relative_y[end_idx] = delta_z / 128.0
            elif organ["sub_type"] == 2:  # buff
                status = organ["status"]
                buff_idx = 0
                target_statuses[buff_idx] = status  # 记录status
                
                if status == 0:  # 不可获取
                    target_distances[buff_idx] = -1
                    continue
                    
                # 如果状态为1（可获取），使用明确的位置信息
                if status == 1:
                    buff_x = organ["pos"]["x"]
                    buff_z = organ["pos"]["z"]
                    # 计算曼哈顿距离
                    distance = abs(buff_x - cur_pos[0]) + abs(buff_z - cur_pos[1])
                    target_distances[buff_idx] = distance
                    # 计算相对位置
                    target_relative_x[buff_idx] = (buff_x - cur_pos[0]) / 128.0
                    target_relative_y[buff_idx] = (buff_z - cur_pos[1]) / 128.0
                    
                # 如果状态为-1（视野外），使用相对位置预测
                elif status == -1:
                    relative_pos = organ["relative_pos"]
                    buff_dir = RelativeDirection[relative_pos["direction"]]
                    buff_dis = RelativeDistance[relative_pos["l2_distance"]]
                    
                    # 使用与end_pos预测相同的逻辑
                    distance = buff_dis * 20
                    theta = DirectionAngles[buff_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))
                    
                    # 预测终点位置
                    predicted_x = max(0, min(128, round(cur_pos[0] + delta_x)))
                    predicted_z = max(0, min(128, round(cur_pos[1] + delta_z)))
                    
                    # 计算曼哈顿距离
                    distance = abs(predicted_x - cur_pos[0]) + abs(predicted_z - cur_pos[1])
                    target_distances[buff_idx] = distance
                    # 计算相对位置
                    target_relative_x[buff_idx] = delta_x / 128.0
                    target_relative_y[buff_idx] = delta_z / 128.0
                    
        return target_distances, target_statuses, target_relative_x, target_relative_y

    def get_top3_objects_feature(self, organs, cur_pos, frame_state):
        """
        使用距离计算选出top-3个最应关注的物件，整合他们的特征
        返回维度为6*3的特征向量
        """
        # 创建物件信息列表
        objects_info = []
        
        for organ in organs:
            if organ["sub_type"] == 1:  # 宝箱类型
                config_id = organ["config_id"]
                status = organ["status"]
                
                if status == 0:  # 跳过不可获取的物件
                    continue
                    
                # 确定目标位置
                if status == 1:  # 可获取，使用明确位置
                    target_pos = (organ["pos"]["x"], organ["pos"]["z"])
                elif status == -1:  # 视野外，使用相对位置预测
                    relative_pos = organ["relative_pos"]
                    treasure_dir = RelativeDirection[relative_pos["direction"]]
                    treasure_dis = RelativeDistance[relative_pos["l2_distance"]]
                    
                    distance = treasure_dis * 20
                    theta = DirectionAngles[treasure_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))
                    
                    target_pos = (
                        max(0, min(128, round(cur_pos[0] + delta_x))),
                        max(0, min(128, round(cur_pos[1] + delta_z)))
                    )
                else:
                    continue
                
                # 使用 _get_pos_feature 计算特征
                feature_target_pos = self._get_pos_feature(status != -1, cur_pos, target_pos)
                
                objects_info.append({
                    'config_id': config_id,
                    'type': 1,  # 宝箱
                    'status': status,
                    'feature': feature_target_pos
                })
                
            elif organ["sub_type"] == 2:  # buff类型
                status = organ["status"]
                
                if status == 0:  # 跳过不可获取的物件
                    continue
                    
                # 确定目标位置
                if status == 1:  # 可获取，使用明确位置
                    target_pos = (organ["pos"]["x"], organ["pos"]["z"])
                elif status == -1:  # 视野外，使用相对位置预测
                    relative_pos = organ["relative_pos"]
                    buff_dir = RelativeDirection[relative_pos["direction"]]
                    buff_dis = RelativeDistance[relative_pos["l2_distance"]]
                    
                    distance = buff_dis * 20
                    theta = DirectionAngles[buff_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))
                    
                    target_pos = (
                        max(0, min(128, round(cur_pos[0] + delta_x))),
                        max(0, min(128, round(cur_pos[1] + delta_z)))
                    )
                else:
                    continue
                
                # 使用 _get_pos_feature 计算特征
                feature_target_pos = self._get_pos_feature(status != -1, cur_pos, target_pos)
                
                objects_info.append({
                    'config_id': 0,  # buff固定为0
                    'type': 2,  # buff
                    'status': status,
                    'feature': feature_target_pos
                })
                
            elif organ["sub_type"] == 4:  # 终点类型
                status = organ["status"]
                
                if status == 0:  # 跳过不可获取的物件
                    continue
                    
                # 确定目标位置
                if status == 1:  # 可获取，使用明确位置
                    target_pos = (organ["pos"]["x"], organ["pos"]["z"])
                elif status == -1:  # 视野外，使用相对位置预测
                    relative_pos = organ["relative_pos"]
                    end_dir = RelativeDirection[relative_pos["direction"]]
                    end_dis = RelativeDistance[relative_pos["l2_distance"]]
                    
                    distance = end_dis * 20
                    theta = DirectionAngles[end_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))
                    
                    target_pos = (
                        max(0, min(128, round(cur_pos[0] + delta_x))),
                        max(0, min(128, round(cur_pos[1] + delta_z)))
                    )
                else:
                    continue
                
                # 使用 _get_pos_feature 计算特征
                feature_target_pos = self._get_pos_feature(status != -1, cur_pos, target_pos)
                
                objects_info.append({
                    'config_id': 14,  # 终点固定为14
                    'type': 4,  # 终点
                    'status': status,
                    'feature': feature_target_pos
                })
        
        # 按优先级排序，选择最优先的3个
        # 优先级规则：status == 1 优先，但终点优先度最低
        def get_priority(obj):
            # status = 0 直接最低优先级
            if obj['status'] == 0:
                return (3, obj['feature'][5])  # 使用距离作为第二优先级
            
            # 检查宝箱收集情况
            treasure_collected = frame_state[0]['score_info'].get('treasure_collected_count', 0)
            # 动态计算宝箱总数（从organs中统计sub_type==1的宝箱）
            total_treasures = sum(1 for organ in organs if organ["sub_type"] == 1)
            has_available_treasures = treasure_collected < total_treasures
            
            # 基础优先级：status == 1 的物件优先
            base_priority = 0 if obj['status'] == 1 else 1
            
            # 如果是终点，根据宝箱收集情况调整优先级
            if obj['type'] == 4:  # 终点
                if has_available_treasures:
                    # 还有宝箱时，终点优先级较低
                    base_priority += 1
                else:
                    # 没有宝箱时，终点优先级最高
                    base_priority = -1
            
            # 在相同优先级内，按距离排序
            return (base_priority, obj['feature'][5])  # feature[5]是距离
        
        objects_info.sort(key=get_priority)
        top3_objects = objects_info[:3]
        
        # 创建6*3的特征向量
        feature_vector = np.zeros((6, 3), dtype=np.float32)
        
        for i, obj in enumerate(top3_objects):
            if i >= 3:  # 最多3个物件
                break
                
            # 使用 _get_pos_feature 返回的6维特征
            feature_vector[:, i] = obj['feature']
        
        return feature_vector



