#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from agent_ppo.conf.conf import Config
from kaiwu_agent.utils.common_func import create_cls, attached
import numpy as np
from typing import List, Optional
# The create_cls function is used to dynamically create a class.
# The first parameter of the function is the type name, and the remaining parameters are the attributes of the class.
# The default value of the attribute should be set to None.
# create_cls函数用于动态创建一个类，函数第一个参数为类型名称，剩余参数为类的属性，属性默认值应设为None
ObsData = create_cls(
    "ObsData",
    feature=None,
    legal_action=None,
    reward=None,
)


ActData = create_cls(
    "ActData",
    probs=None,
    value=None,
    target=None,
    predict=None,
    action=None,
    prob=None,
)

SampleData = create_cls("SampleData", npdata=None)

RelativeDistance = {
    "RELATIVE_DISTANCE_NONE": 0,
    "VerySmall": 1,
    "Small": 2,
    "Medium": 3,
    "Large": 4,
    "VeryLarge": 5,
}


RelativeDirection = {
    "East": 1,
    "NorthEast": 2,
    "North": 3,
    "NorthWest": 4,
    "West": 5,
    "SouthWest": 6,
    "South": 7,
    "SouthEast": 8,
}

DirectionAngles = {
    1: 0,
    2: 45,
    3: 90,
    4: 135,
    5: 180,
    6: 225,
    7: 270,
    8: 315,
}
class TreasureLocker:
    """
    在多宝藏场景下维持“当前锁定宝藏”的 config_id 以及上一帧距离，
    直到该宝藏被捡起(或消失)后再切换到下一枚最近宝藏。
    reward = (prev_dist - curr_dist) * scale
    """

    def __init__(self, scale: float = 0.2):
        self.scale: float = scale
        self.locked_id: Optional[int] = None  # 这里的 locked_id 实际上是 config_id
        self.prev_dist: Optional[float] = None

    # ----------------------- internal helpers --------------------------- #
    def _pick_new_target(self, organs: list, agent_pos) -> None:
        """选取离智能体最近的一枚可收集宝藏作为新目标"""
        candidates = [
            (org["config_id"], Manhattan_distance(agent_pos, org["pos"]))
            for org in organs
            if org["sub_type"] == 1 and org["status"] == 1
        ]
        if candidates:
            self.locked_id, self.prev_dist = min(candidates, key=lambda x: x[1])
        else:
            self.locked_id, self.prev_dist = None, None

    # ----------------------- public API --------------------------------- #
    def update_and_reward(self, organs: list, agent_pos) -> float:
        """
        更新锁定状态并计算 reward。
        第一次锁定或锁定失效后重新锁定时，reward 为 0。
        """
        # 当前锁定目标是否仍然存在？
        locked_organ = next(
            (
                o
                for o in organs
                if o["config_id"] == self.locked_id
                and o["sub_type"] == 1
                and o["status"] == 1
            ),
            None,
        )

        # 需要重新锁定
        if locked_organ is None:
            self._pick_new_target(organs, agent_pos)
            return 0.0

        # 继续追踪同一目标
        curr_dist = Manhattan_distance(agent_pos, locked_organ["pos"])
        reward = (self.prev_dist - curr_dist) * self.scale
        self.prev_dist = curr_dist
        return reward


# 实例化全局 TreasureLocker（在每个 episode 重新创建即可）
locker = TreasureLocker(scale=0.1)

def Manhattan_distance(pos1, pos2):
    """计算两点之间的曼哈顿距离。"""
    p1 = (pos1['x'], pos1['z']) if isinstance(pos1, dict) else pos1
    p2 = (pos2['x'], pos2['z']) if isinstance(pos2, dict) else pos2
    return abs(p1[0] - p2[0]) + abs(p1[1] - p2[1])

def reward_process(obs, extra_info, prev_obs, prev_extra_info, local_memory_map):
    """
    Calculate reward at each environment step.
    """
    # 基础 step 惩罚与记忆地图惩罚
    step_reward = -0.01
    memory_penalty = -local_memory_map[5, 5] if local_memory_map[5, 5] > 0.21 else 0.0
    
    # ---- 开局帧：没有上一状态，直接返回 ----
    if prev_obs is None or prev_extra_info is None:
        return [step_reward + memory_penalty]

    #閃現懲罰
    flash_penalty = 0
    if prev_obs['frame_state']['heroes'][0]['talent']['status'] == 1 and obs['frame_state']['heroes'][0]['talent']['status'] == 0:
        flash_penalty = -0.5
    
    stop_penalty = 0
    #罰站懲罰
    if prev_obs['frame_state']['heroes'][0]['pos'] == obs['frame_state']['heroes'][0]['pos']:
        stop_penalty = -0.5


    # ---- 收集到宝藏的瞬时大奖励 ----
    if (
        "score_info" in obs
        and "score_info" in prev_obs
        and prev_obs["score_info"]["treasure_collected_count"]
        < obs["score_info"]["treasure_collected_count"]
    ):
        print("Log: Treasure collected! Reward: 5.0")
        return [5.0]

    # 初始化奖励项
    potential_reward = 0.0
    treasure_reward = 0.0

    # ---- 信息完整性检查 ----
    if all(k in extra_info for k in ("game_info", "frame_state")) and all(
        k in prev_extra_info for k in ("game_info", "frame_state")
    ):
        cur_pos = (
            extra_info["game_info"]["pos"]["x"],
            extra_info["game_info"]["pos"]["z"],
        )
        prev_pos = (
            prev_extra_info["game_info"]["pos"]["x"],
            prev_extra_info["game_info"]["pos"]["z"],
        )

        # -------- 判断是否已收集完全部宝藏 -------- #
        all_treasures_collected = True
        for organ in extra_info["frame_state"].get("organs", []):
            if organ["sub_type"] == 1 and organ["status"] == 1:
                all_treasures_collected = False
                break

        # -------- phase 2：朝终点移动 -------- #
        if all_treasures_collected:
            end_pos = extra_info["game_info"].get("end_pos")
            if end_pos:
                cur_end_dist = Manhattan_distance(cur_pos, end_pos)
                prev_end_dist = Manhattan_distance(prev_pos, end_pos)
                potential_reward += (prev_end_dist - cur_end_dist) * 0.1

        # -------- phase 1：收集宝藏 -------- #
        else:
            organs = extra_info["frame_state"].get("organs", [])
            treasure_reward = locker.update_and_reward(organs, cur_pos)

    # ---- Buff 收集奖励 ----
    if (
        "score_info" in obs
        and "score_info" in prev_obs
        and obs["score_info"]["buff_count"] > prev_obs["score_info"]["buff_count"]
    ):
        print("BUFF")
        potential_reward += 0.5 ** obs["score_info"]["buff_count"]

    total_reward = step_reward + memory_penalty + potential_reward + treasure_reward + flash_penalty + stop_penalty
    return [total_reward]


class SampleManager:
    def __init__(
        self,
        gamma=0.99,
        tdlambda=0.95,
    ):
        self.gamma = Config.GAMMA
        self.tdlambda = Config.TDLAMBDA

        self.feature = []
        self.probs = []
        self.actions = []
        self.reward = []
        self.value = []
        self.adv = []
        self.tdlamret = []
        self.legal_action = []
        self.count = 0
        self.samples = []

    def add(self, feature, legal_action, prob, action, value, reward):
        self.feature.append(feature)
        self.legal_action.append(legal_action)
        self.probs.append(prob)
        self.actions.append(action)
        self.value.append(value)
        self.reward.append(reward)
        self.adv.append(np.zeros_like(value))
        self.tdlamret.append(np.zeros_like(value))
        self.count += 1

    def add_last_reward(self, reward):
        self.reward.append(reward)
        self.value.append(np.zeros_like(reward))

    def update_sample_info(self):
        last_gae = 0
        for i in range(self.count - 1, -1, -1):
            reward = self.reward[i + 1]
            next_val = self.value[i + 1]
            val = self.value[i]
            delta = reward + next_val * self.gamma - val
            last_gae = delta + self.gamma * self.tdlambda * last_gae
            self.adv[i] = last_gae
            self.tdlamret[i] = last_gae + val

    def sample_process(self, feature, legal_action, prob, action, value, reward):
        self.add(feature, legal_action, prob, action, value, reward)

    def process_last_frame(self, reward):
        self.add_last_reward(reward)
        # 发送前的后向传递更新
        # Backward pass updates before sending
        self.update_sample_info()
        self.samples = self._get_game_data()

    def get_game_data(self):
        ret = self.samples
        self.samples = []
        return ret

    def _get_game_data(self):
        feature = np.array(self.feature).transpose()
        probs = np.array(self.probs).transpose()
        actions = np.array(self.actions).transpose()
        reward = np.array(self.reward[:-1]).transpose()
        value = np.array(self.value[:-1]).transpose()
        legal_action = np.array(self.legal_action).transpose()
        adv = np.array(self.adv).transpose()
        tdlamret = np.array(self.tdlamret).transpose()

        data = np.concatenate([feature, reward, value, tdlamret, adv, actions, probs, legal_action]).transpose()

        samples = []
        for i in range(0, self.count):
            samples.append(SampleData(npdata=data[i].astype(np.float32)))

        return samples


@attached
def SampleData2NumpyData(g_data):
    return g_data.npdata


@attached
def NumpyData2SampleData(s_data):
    return SampleData(npdata=s_data)
